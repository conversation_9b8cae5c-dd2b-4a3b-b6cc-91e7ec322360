# 专辑唱片MP4视频生成器

这个工具可以将音频文件和专辑封面图片合成为MP4视频，适合制作专辑唱片视频。

## 功能特点

- 🎵 支持多种音频格式（MP3、WAV、FLAC等）
- 🖼️ 支持多种图片格式（JPG、PNG、BMP等）
- 📹 生成高质量MP4视频（默认1920x1080分辨率）
- 🔧 可自定义视频参数（分辨率、帧率等）
- 📐 自动调整图片尺寸，保持宽高比
- ⚡ 快速处理，支持长时间音频

## 文件说明

- `generate_album_video.py` - 主程序脚本
- `requirements.txt` - Python依赖包列表
- `run_generator.bat` - 一键运行批处理文件
- `README.md` - 使用说明文档

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 方法1：使用默认参数（推荐）

直接运行批处理文件：
```bash
run_generator.bat
```

或者运行Python脚本：
```bash
python generate_album_video.py
```

默认会使用当前目录下的：
- 音频文件：`guisu.mp3`
- 专辑封面：`fangjian10.jpg`
- 输出文件：`album_video.mp4`

### 方法2：自定义参数

```bash
python generate_album_video.py --audio 你的音频.mp3 --image 你的封面.jpg --output 输出视频.mp4
```

### 完整参数说明

```bash
python generate_album_video.py [选项]

选项:
  --audio, -a     音频文件路径 (默认: guisu.mp3)
  --image, -i     专辑封面图片路径 (默认: fangjian10.jpg)
  --output, -o    输出视频文件路径 (默认: album_video.mp4)
  --width, -w     视频宽度 (默认: 1920)
  --height        视频高度 (默认: 1080)
  --fps, -f       视频帧率 (默认: 30)
  --help          显示帮助信息
```

## 使用示例

### 示例1：基本使用
```bash
python generate_album_video.py
```

### 示例2：指定文件
```bash
python generate_album_video.py --audio "我的歌曲.mp3" --image "专辑封面.jpg" --output "我的视频.mp4"
```

### 示例3：自定义分辨率
```bash
python generate_album_video.py --width 1280 --height 720 --fps 24
```

## 输出信息

程序运行时会显示：
- 🎵 音频文件信息和时长
- 🖼️ 图片处理进度
- 📹 视频合成进度
- ✅ 最终输出文件信息

## 注意事项

1. **文件格式支持**：
   - 音频：MP3、WAV、FLAC、AAC、M4A等
   - 图片：JPG、JPEG、PNG、BMP、TIFF等

2. **性能优化**：
   - 图片会自动调整到合适尺寸
   - 长音频文件可能需要较长处理时间
   - 建议使用SSD硬盘以提高处理速度

3. **系统要求**：
   - Python 3.7+
   - 足够的磁盘空间（约为音频文件大小的2-3倍）

## 故障排除

### 常见问题

1. **ModuleNotFoundError**: 请确保已安装所有依赖包
   ```bash
   pip install -r requirements.txt
   ```

2. **文件不存在错误**: 检查音频和图片文件路径是否正确

3. **内存不足**: 对于很长的音频文件，可能需要更多内存

4. **编码错误**: 确保文件名不包含特殊字符

### 获取帮助

运行以下命令查看详细帮助：
```bash
python generate_album_video.py --help
```

## 技术说明

- 使用 MoviePy 库进行视频处理
- 使用 Pillow 库进行图片处理
- 支持硬件加速编码（如果可用）
- 输出格式：H.264视频编码 + AAC音频编码

---

🎉 享受您的专辑视频制作过程！
