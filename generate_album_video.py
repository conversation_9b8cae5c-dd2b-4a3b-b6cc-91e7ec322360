#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专辑唱片MP4视频生成器
将音频文件和专辑封面图片合成为MP4视频
"""

import os
import sys
from moviepy.editor import ImageClip, AudioFileClip, CompositeVideoClip
from PIL import Image
import argparse

def check_files_exist(audio_file, image_file):
    """检查音频和图片文件是否存在"""
    if not os.path.exists(audio_file):
        print(f"错误：音频文件 '{audio_file}' 不存在")
        return False
    if not os.path.exists(image_file):
        print(f"错误：图片文件 '{image_file}' 不存在")
        return False
    return True

def get_audio_duration(audio_file):
    """获取音频文件的时长"""
    try:
        audio = AudioFileClip(audio_file)
        duration = audio.duration
        audio.close()
        return duration
    except Exception as e:
        print(f"错误：无法读取音频文件 '{audio_file}': {e}")
        return None

def resize_image_to_video_size(image_file, target_width=1920, target_height=1080):
    """调整图片尺寸以适应视频分辨率，保持宽高比"""
    try:
        with Image.open(image_file) as img:
            # 获取原始尺寸
            original_width, original_height = img.size
            
            # 计算缩放比例，保持宽高比
            width_ratio = target_width / original_width
            height_ratio = target_height / original_height
            scale_ratio = min(width_ratio, height_ratio)
            
            # 计算新尺寸
            new_width = int(original_width * scale_ratio)
            new_height = int(original_height * scale_ratio)
            
            # 调整图片尺寸
            resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # 创建黑色背景
            background = Image.new('RGB', (target_width, target_height), (0, 0, 0))
            
            # 将调整后的图片居中放置在背景上
            x_offset = (target_width - new_width) // 2
            y_offset = (target_height - new_height) // 2
            background.paste(resized_img, (x_offset, y_offset))
            
            # 保存临时文件
            temp_image_file = "temp_resized_image.jpg"
            background.save(temp_image_file, "JPEG", quality=95)
            
            return temp_image_file
            
    except Exception as e:
        print(f"错误：无法处理图片文件 '{image_file}': {e}")
        return None

def create_album_video(audio_file, image_file, output_file="album_video.mp4", 
                      video_width=1920, video_height=1080, fps=30):
    """创建专辑视频"""
    
    print("开始生成专辑视频...")
    
    # 检查文件是否存在
    if not check_files_exist(audio_file, image_file):
        return False
    
    # 获取音频时长
    print("正在分析音频文件...")
    audio_duration = get_audio_duration(audio_file)
    if audio_duration is None:
        return False
    
    print(f"音频时长: {audio_duration:.2f} 秒")
    
    try:
        # 调整图片尺寸
        print("正在处理专辑封面...")
        resized_image_file = resize_image_to_video_size(image_file, video_width, video_height)
        if resized_image_file is None:
            return False
        
        # 加载音频
        print("正在加载音频...")
        audio = AudioFileClip(audio_file)
        
        # 创建图片剪辑，时长与音频相同
        print("正在创建视频剪辑...")
        image_clip = ImageClip(resized_image_file, duration=audio_duration)
        image_clip = image_clip.set_fps(fps)
        
        # 合成最终视频
        print("正在合成视频...")
        final_video = image_clip.set_audio(audio)
        
        # 输出视频
        print(f"正在输出视频到 '{output_file}'...")
        final_video.write_videofile(
            output_file,
            fps=fps,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            verbose=False,
            logger=None
        )
        
        # 清理资源
        audio.close()
        image_clip.close()
        final_video.close()
        
        # 删除临时文件
        if os.path.exists(resized_image_file):
            os.remove(resized_image_file)
        
        print(f"✅ 视频生成成功！输出文件: {output_file}")
        print(f"📊 视频信息:")
        print(f"   - 分辨率: {video_width}x{video_height}")
        print(f"   - 帧率: {fps} fps")
        print(f"   - 时长: {audio_duration:.2f} 秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成视频时发生错误: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='生成专辑唱片MP4视频')
    parser.add_argument('--audio', '-a', default='guisu.mp3', 
                       help='音频文件路径 (默认: guisu.mp3)')
    parser.add_argument('--image', '-i', default='fangjian10.jpg', 
                       help='专辑封面图片路径 (默认: fangjian10.jpg)')
    parser.add_argument('--output', '-o', default='album_video.mp4', 
                       help='输出视频文件路径 (默认: album_video.mp4)')
    parser.add_argument('--width', '-w', type=int, default=1920, 
                       help='视频宽度 (默认: 1920)')
    parser.add_argument('--height', type=int, default=1080,
                       help='视频高度 (默认: 1080)')
    parser.add_argument('--fps', '-f', type=int, default=30, 
                       help='视频帧率 (默认: 30)')
    
    args = parser.parse_args()
    
    print("🎵 专辑唱片MP4视频生成器")
    print("=" * 50)
    print(f"音频文件: {args.audio}")
    print(f"专辑封面: {args.image}")
    print(f"输出文件: {args.output}")
    print(f"视频尺寸: {args.width}x{args.height}")
    print(f"帧率: {args.fps} fps")
    print("=" * 50)
    
    success = create_album_video(
        audio_file=args.audio,
        image_file=args.image,
        output_file=args.output,
        video_width=args.width,
        video_height=args.height,
        fps=args.fps
    )
    
    if success:
        print("\n🎉 任务完成！")
    else:
        print("\n❌ 任务失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
