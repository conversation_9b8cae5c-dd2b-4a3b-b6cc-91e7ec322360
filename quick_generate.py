#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速专辑视频生成器
简化版本，自动检测音频和图片文件
"""

import os
import glob
from generate_album_video import create_album_video

def find_audio_files():
    """查找音频文件"""
    audio_extensions = ['*.mp3', '*.wav', '*.flac', '*.aac', '*.m4a', '*.ogg']
    audio_files = []
    for ext in audio_extensions:
        audio_files.extend(glob.glob(ext, recursive=False))
    return audio_files

def find_image_files():
    """查找图片文件"""
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.gif']
    image_files = []
    for ext in image_extensions:
        image_files.extend(glob.glob(ext, recursive=False))
    return image_files

def main():
    print("🎵 快速专辑视频生成器")
    print("=" * 40)
    
    # 查找音频文件
    audio_files = find_audio_files()
    if not audio_files:
        print("❌ 未找到音频文件！")
        print("请确保当前目录包含音频文件（支持格式：mp3, wav, flac, aac, m4a, ogg）")
        return
    
    # 查找图片文件
    image_files = find_image_files()
    if not image_files:
        print("❌ 未找到图片文件！")
        print("请确保当前目录包含图片文件（支持格式：jpg, jpeg, png, bmp, tiff, gif）")
        return
    
    # 选择文件
    if len(audio_files) == 1:
        audio_file = audio_files[0]
        print(f"✅ 找到音频文件: {audio_file}")
    else:
        print("📁 找到多个音频文件:")
        for i, file in enumerate(audio_files, 1):
            print(f"  {i}. {file}")
        try:
            choice = int(input("请选择音频文件编号: ")) - 1
            audio_file = audio_files[choice]
        except (ValueError, IndexError):
            print("❌ 无效选择，使用第一个音频文件")
            audio_file = audio_files[0]
    
    if len(image_files) == 1:
        image_file = image_files[0]
        print(f"✅ 找到图片文件: {image_file}")
    else:
        print("🖼️ 找到多个图片文件:")
        for i, file in enumerate(image_files, 1):
            print(f"  {i}. {file}")
        try:
            choice = int(input("请选择图片文件编号: ")) - 1
            image_file = image_files[choice]
        except (ValueError, IndexError):
            print("❌ 无效选择，使用第一个图片文件")
            image_file = image_files[0]
    
    # 生成输出文件名
    base_name = os.path.splitext(audio_file)[0]
    output_file = f"{base_name}_album_video.mp4"
    
    print("\n" + "=" * 40)
    print(f"🎵 音频文件: {audio_file}")
    print(f"🖼️ 图片文件: {image_file}")
    print(f"📹 输出文件: {output_file}")
    print("=" * 40)
    
    # 生成视频
    success = create_album_video(
        audio_file=audio_file,
        image_file=image_file,
        output_file=output_file
    )
    
    if success:
        print(f"\n🎉 视频生成完成！")
        print(f"📁 输出文件: {output_file}")
        print(f"💡 提示: 您可以使用视频播放器打开查看效果")
    else:
        print("\n❌ 视频生成失败！")

if __name__ == "__main__":
    main()
